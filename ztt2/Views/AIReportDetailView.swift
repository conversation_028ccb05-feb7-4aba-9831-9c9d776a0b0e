//
//  AIReportDetailView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import SwiftUI

/**
 * AI报告详情页面
 * 显示AI生成的分析报告内容
 */
struct AIReportDetailView: View {
    
    // MARK: - Properties
    
    let report: AIAnalysisReport
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 报告头部信息
                    reportHeaderCard
                    
                    // 报告内容
                    reportContentCard
                }
                .padding()
            }
            .navigationTitle("AI分析报告")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingShareSheet = true
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
    }
    
    // MARK: - View Components
    
    /// 报告头部信息卡片
    private var reportHeaderCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 报告类型和时间
            HStack {
                Label(report.reportType.displayName, systemImage: reportTypeIcon)
                    .font(.headline)
                    .foregroundColor(.blue)
                
                Spacer()
                
                Text(DateFormatter.fullDateTime.string(from: report.createdAt))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 成员信息
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("分析对象")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("\(report.memberName) · \(report.memberRole) · \(report.memberAge)岁")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
            }
            
            Divider()
            
            // 报告说明
            Text(reportDescription)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    /// 报告内容卡片
    private var reportContentCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("分析内容")
                .font(.headline)
            
            // 使用Markdown渲染报告内容
            MarkdownText(content: report.content)
                .font(.body)
                .lineSpacing(4)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Computed Properties
    
    /// 报告类型图标
    private var reportTypeIcon: String {
        switch report.reportType {
        case .behaviorAnalysis:
            return "chart.line.uptrend.xyaxis"
        case .growthReport:
            return "heart.text.square"
        }
    }
    
    /// 报告描述
    private var reportDescription: String {
        switch report.reportType {
        case .behaviorAnalysis:
            return "基于积分记录数据，运用行为分析学理论生成的专业分析报告"
        case .growthReport:
            return "基于成长日记内容，运用儿童心理学理论生成的专业成长报告"
        }
    }
    
    // MARK: - Helper Methods
    
    /// 生成分享文本
    private func generateShareText() -> String {
        let header = """
        【\(report.reportType.displayName)】
        分析对象：\(report.memberName)（\(report.memberRole)，\(report.memberAge)岁）
        生成时间：\(DateFormatter.fullDateTime.string(from: report.createdAt))
        
        """
        
        let content = report.content
        
        let footer = """
        
        
        ——————————————————
        由转团团AI分析功能生成
        """
        
        return header + content + footer
    }
}

// MARK: - CoreData版本的AIReportDetailView

/**
 * 基于CoreData AIReport实体的报告详情页面
 */
struct AIReportDetailCoreDataView: View {
    
    let aiReport: AIReport
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 报告头部信息
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Label(aiReport.reportTypeDisplayName, systemImage: reportTypeIcon)
                                .font(.headline)
                                .foregroundColor(.blue)
                            
                            Spacer()
                            
                            Text(aiReport.formattedCreatedAt)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        if let member = aiReport.member {
                            HStack(spacing: 12) {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("分析对象")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    
                                    Text("\(member.displayName) · \(member.roleDisplayName) · \(member.age)岁")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                }
                                
                                Spacer()
                            }
                        }
                        
                        if let summary = aiReport.inputDataSummary {
                            Divider()
                            
                            Text(summary)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // 报告内容
                    VStack(alignment: .leading, spacing: 16) {
                        Text("分析内容")
                            .font(.headline)
                        
                        MarkdownText(content: aiReport.content ?? "")
                            .font(.body)
                            .lineSpacing(4)
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                }
                .padding()
            }
            .navigationTitle("AI分析报告")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingShareSheet = true
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
    }
    
    private var reportTypeIcon: String {
        switch aiReport.reportType ?? "analysis" {
        case "analysis":
            return "chart.line.uptrend.xyaxis"
        case "growth":
            return "heart.text.square"
        default:
            return "doc.text"
        }
    }
    
    private func generateShareText() -> String {
        let header = """
        【\(aiReport.reportTypeDisplayName)】
        分析对象：\(aiReport.member?.displayName ?? "未知")
        生成时间：\(aiReport.formattedCreatedAt)
        
        """
        
        let content = aiReport.content ?? ""
        
        let footer = """
        
        
        ——————————————————
        由转团团AI分析功能生成
        """
        
        return header + content + footer
    }
}

// MARK: - Supporting Views

/**
 * 增强的Markdown文本渲染器
 * 支持iOS 15.6+的markdown格式渲染
 */
struct MarkdownText: View {
    let content: String

    init(content: String) {
        self.content = content
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(parseMarkdownContent(), id: \.id) { element in
                renderMarkdownElement(element)
            }
        }
        .textSelection(.enabled)
    }

    // MARK: - Markdown解析

    /**
     * 解析markdown内容为结构化元素
     */
    private func parseMarkdownContent() -> [MarkdownElement] {
        let lines = content.components(separatedBy: .newlines)
        var elements: [MarkdownElement] = []
        var currentParagraph: [String] = []

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)

            if trimmedLine.isEmpty {
                // 空行，结束当前段落
                if !currentParagraph.isEmpty {
                    elements.append(MarkdownElement(
                        id: UUID(),
                        type: .paragraph,
                        content: currentParagraph.joined(separator: " ")
                    ))
                    currentParagraph.removeAll()
                }
            } else if trimmedLine.hasPrefix("# ") {
                // 一级标题
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading1,
                    content: String(trimmedLine.dropFirst(2))
                ))
            } else if trimmedLine.hasPrefix("## ") {
                // 二级标题
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading2,
                    content: String(trimmedLine.dropFirst(3))
                ))
            } else if trimmedLine.hasPrefix("### ") {
                // 三级标题
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading3,
                    content: String(trimmedLine.dropFirst(4))
                ))
            } else if trimmedLine.hasPrefix("- ") || trimmedLine.hasPrefix("* ") {
                // 列表项
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .listItem,
                    content: String(trimmedLine.dropFirst(2))
                ))
            } else {
                // 普通文本，添加到当前段落
                currentParagraph.append(trimmedLine)
            }
        }

        // 处理最后的段落
        finalizeParagraph(&currentParagraph, &elements)

        return elements
    }

    /**
     * 完成当前段落的处理
     */
    private func finalizeParagraph(_ currentParagraph: inout [String], _ elements: inout [MarkdownElement]) {
        if !currentParagraph.isEmpty {
            elements.append(MarkdownElement(
                id: UUID(),
                type: .paragraph,
                content: currentParagraph.joined(separator: " ")
            ))
            currentParagraph.removeAll()
        }
    }

    // MARK: - 渲染方法

    /**
     * 渲染markdown元素
     */
    @ViewBuilder
    private func renderMarkdownElement(_ element: MarkdownElement) -> some View {
        switch element.type {
        case .heading1:
            Text(element.content)
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.primary)
                .padding(.vertical, 4)
        case .heading2:
            Text(element.content)
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .padding(.vertical, 3)
        case .heading3:
            Text(element.content)
                .font(.title3)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .padding(.vertical, 2)
        case .paragraph:
            Text(parseInlineMarkdown(element.content))
                .font(.body)
                .foregroundColor(.primary)
                .lineSpacing(2)
                .padding(.vertical, 1)
        case .listItem:
            HStack(alignment: .top, spacing: 8) {
                Text("•")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .padding(.top, 1)
                Text(parseInlineMarkdown(element.content))
                    .font(.body)
                    .foregroundColor(.primary)
                    .lineSpacing(2)
                Spacer()
            }
            .padding(.leading, 16)
            .padding(.vertical, 1)
        }
    }

    /**
     * 解析行内markdown格式（粗体、斜体等）
     */
    private func parseInlineMarkdown(_ text: String) -> AttributedString {
        var attributedString = AttributedString(text)

        // 处理粗体 **text**
        let boldPattern = #"\*\*(.*?)\*\*"#
        if let regex = try? NSRegularExpression(pattern: boldPattern) {
            let matches = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
            for match in matches.reversed() {
                if let range = Range(match.range, in: text) {
                    let boldText = String(text[range])
                    let content = String(boldText.dropFirst(2).dropLast(2))
                    if let attributedRange = Range(match.range, in: attributedString) {
                        attributedString.replaceSubrange(attributedRange, with: AttributedString(content))
                        if let newRange = attributedString.range(of: content) {
                            attributedString[newRange].font = .body.bold()
                        }
                    }
                }
            }
        }

        return attributedString
    }
}

// MARK: - Markdown数据模型

/**
 * Markdown元素类型
 */
enum MarkdownElementType {
    case heading1
    case heading2
    case heading3
    case paragraph
    case listItem
}

/**
 * Markdown元素
 */
struct MarkdownElement {
    let id: UUID
    let type: MarkdownElementType
    let content: String
}

/**
 * 分享功能
 */
struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - DateFormatter Extension

extension DateFormatter {
    static let fullDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
}
