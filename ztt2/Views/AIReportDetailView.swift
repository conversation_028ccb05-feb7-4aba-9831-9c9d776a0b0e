//
//  AIReportDetailView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import SwiftUI

/**
 * AI报告详情页面
 * 显示AI生成的分析报告内容
 */
struct AIReportDetailView: View {
    
    // MARK: - Properties
    
    let report: AIAnalysisReport
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 报告头部信息
                    reportHeaderCard
                    
                    // 报告内容
                    reportContentCard
                }
                .padding()
            }
            .navigationTitle("AI分析报告")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingShareSheet = true
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
    }
    
    // MARK: - View Components
    
    /// 报告头部信息卡片
    private var reportHeaderCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 报告类型和时间
            HStack {
                Label(report.reportType.displayName, systemImage: reportTypeIcon)
                    .font(.headline)
                    .foregroundColor(.blue)
                
                Spacer()
                
                Text(DateFormatter.fullDateTime.string(from: report.createdAt))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 成员信息
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("分析对象")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("\(report.memberName) · \(report.memberRole) · \(report.memberAge)岁")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
            }
            
            Divider()
            
            // 报告说明
            Text(reportDescription)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    /// 报告内容卡片
    private var reportContentCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("分析内容")
                .font(.headline)
            
            // 使用Markdown渲染报告内容
            MarkdownText(content: report.content)
                .font(.body)
                .lineSpacing(4)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Computed Properties
    
    /// 报告类型图标
    private var reportTypeIcon: String {
        switch report.reportType {
        case .behaviorAnalysis:
            return "chart.line.uptrend.xyaxis"
        case .growthReport:
            return "heart.text.square"
        }
    }
    
    /// 报告描述
    private var reportDescription: String {
        switch report.reportType {
        case .behaviorAnalysis:
            return "基于积分记录数据，运用行为分析学理论生成的专业分析报告"
        case .growthReport:
            return "基于成长日记内容，运用儿童心理学理论生成的专业成长报告"
        }
    }
    
    // MARK: - Helper Methods
    
    /// 生成分享文本
    private func generateShareText() -> String {
        let header = """
        【\(report.reportType.displayName)】
        分析对象：\(report.memberName)（\(report.memberRole)，\(report.memberAge)岁）
        生成时间：\(DateFormatter.fullDateTime.string(from: report.createdAt))
        
        """
        
        let content = report.content
        
        let footer = """
        
        
        ——————————————————
        由转团团AI分析功能生成
        """
        
        return header + content + footer
    }
}

// MARK: - CoreData版本的AIReportDetailView

/**
 * 基于CoreData AIReport实体的报告详情页面
 */
struct AIReportDetailCoreDataView: View {
    
    let aiReport: AIReport
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 报告头部信息
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Label(aiReport.reportTypeDisplayName, systemImage: reportTypeIcon)
                                .font(.headline)
                                .foregroundColor(.blue)
                            
                            Spacer()
                            
                            Text(aiReport.formattedCreatedAt)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        if let member = aiReport.member {
                            HStack(spacing: 12) {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("分析对象")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    
                                    Text("\(member.displayName) · \(member.roleDisplayName) · \(member.age)岁")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                }
                                
                                Spacer()
                            }
                        }
                        
                        if let summary = aiReport.inputDataSummary {
                            Divider()
                            
                            Text(summary)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // 报告内容
                    VStack(alignment: .leading, spacing: 16) {
                        Text("分析内容")
                            .font(.headline)
                        
                        MarkdownText(content: aiReport.content ?? "")
                            .font(.body)
                            .lineSpacing(4)
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                }
                .padding()
            }
            .navigationTitle("AI分析报告")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingShareSheet = true
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
    }
    
    private var reportTypeIcon: String {
        switch aiReport.reportType ?? "analysis" {
        case "analysis":
            return "chart.line.uptrend.xyaxis"
        case "growth":
            return "heart.text.square"
        default:
            return "doc.text"
        }
    }
    
    private func generateShareText() -> String {
        let header = """
        【\(aiReport.reportTypeDisplayName)】
        分析对象：\(aiReport.member?.displayName ?? "未知")
        生成时间：\(aiReport.formattedCreatedAt)
        
        """
        
        let content = aiReport.content ?? ""
        
        let footer = """
        
        
        ——————————————————
        由转团团AI分析功能生成
        """
        
        return header + content + footer
    }
}

// MARK: - Supporting Views

/**
 * 简单的Markdown文本渲染器
 */
struct MarkdownText: View {
    let content: String

    init(content: String) {
        self.content = content
    }

    var body: some View {
        // 这里使用简单的文本显示，实际项目中可以使用更复杂的Markdown渲染库
        Text(content)
            .textSelection(.enabled)
    }
}

/**
 * 分享功能
 */
struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - DateFormatter Extension

extension DateFormatter {
    static let fullDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
}
