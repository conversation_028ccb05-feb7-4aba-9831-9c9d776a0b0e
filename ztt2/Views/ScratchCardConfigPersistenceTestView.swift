//
//  ScratchCardConfigPersistenceTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI
import CoreData

/**
 * 刮刮卡配置持久化测试视图
 * 用于验证刮刮卡配置弹窗的数据持久化功能是否正常工作
 */
struct ScratchCardConfigPersistenceTestView: View {
    
    @Environment(\.managedObjectContext) private var viewContext
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Member.name, ascending: true)],
        animation: .default
    )
    private var members: FetchedResults<Member>
    
    @State private var selectedMember: Member?
    @State private var showScratchCardConfig = false
    @State private var testResults: [String] = []
    @State private var isRunningTest = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("刮刮卡配置持久化测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 20)
                
                // 测试说明
                testInstructionSection
                
                // 成员选择
                memberSelectionSection
                
                // 测试步骤
                testStepsSection
                
                // 测试结果
                if !testResults.isEmpty {
                    testResultsSection
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .background(Color.gray.opacity(0.05))
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showScratchCardConfig) {
            if let member = selectedMember {
                ScratchCardConfigPopupView(
                    isPresented: $showScratchCardConfig,
                    selectedMember: member,
                    onSave: { configData in
                        handleConfigSave(configData)
                    },
                    onCancel: {
                        showScratchCardConfig = false
                    }
                )
            }
        }
        .onAppear {
            loadTestData()
        }
    }
    
    // MARK: - 子视图组件
    
    /**
     * 测试说明区域
     */
    private var testInstructionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("测试说明")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Text("此测试用于验证刮刮卡配置弹窗的数据持久化功能：")
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("1. 首次打开弹窗应显示默认配置")
                Text("2. 配置并保存后，数据应正确存储")
                Text("3. 再次打开弹窗应加载已保存的配置")
                Text("4. 修改配置后应能正确更新")
            }
            .font(.system(size: 13))
            .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .padding(16)
        .background(Color.blue.opacity(0.1))
        .cornerRadius(12)
    }
    
    /**
     * 成员选择区域
     */
    private var memberSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("选择测试成员")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            if members.isEmpty {
                Text("没有可用的成员，请先添加成员")
                    .font(.system(size: 14))
                    .foregroundColor(.red)
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(members, id: \.self) { member in
                            memberCard(member: member)
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }
        }
    }
    
    /**
     * 成员卡片
     */
    private func memberCard(member: Member) -> some View {
        VStack(spacing: 8) {
            Image(member.avatarImageName)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 50, height: 50)
                .clipShape(Circle())
            
            Text(member.name ?? "未知")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(1)
            
            Text("\(member.currentPoints)积分")
                .font(.system(size: 10))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .padding(12)
        .background(selectedMember == member ? DesignSystem.Colors.primary.opacity(0.2) : Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(selectedMember == member ? DesignSystem.Colors.primary : Color.gray.opacity(0.3), lineWidth: 2)
        )
        .onTapGesture {
            selectedMember = member
            testResults.removeAll()
        }
    }
    
    /**
     * 测试步骤区域
     */
    private var testStepsSection: some View {
        VStack(spacing: 16) {
            // 手动测试按钮
            Button(action: {
                if selectedMember != nil {
                    showScratchCardConfig = true
                }
            }) {
                HStack {
                    Image(systemName: "gear")
                    Text("打开刮刮卡配置")
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(selectedMember != nil ? DesignSystem.Colors.primary : Color.gray)
                .cornerRadius(8)
            }
            .disabled(selectedMember == nil)
            
            // 自动化测试按钮
            Button(action: {
                runAutomatedTest()
            }) {
                HStack {
                    if isRunningTest {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "play.circle")
                    }
                    Text(isRunningTest ? "测试中..." : "运行自动化测试")
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(selectedMember != nil && !isRunningTest ? Color.green : Color.gray)
                .cornerRadius(8)
            }
            .disabled(selectedMember == nil || isRunningTest)
        }
    }
    
    /**
     * 测试结果区域
     */
    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("测试结果")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(testResults, id: \.self) { result in
                        HStack(alignment: .top, spacing: 8) {
                            Image(systemName: result.hasPrefix("✅") ? "checkmark.circle.fill" : 
                                  result.hasPrefix("❌") ? "xmark.circle.fill" : "info.circle.fill")
                                .foregroundColor(result.hasPrefix("✅") ? .green : 
                                               result.hasPrefix("❌") ? .red : .blue)
                                .font(.system(size: 14))
                            
                            Text(result)
                                .font(.system(size: 13))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            
                            Spacer()
                        }
                    }
                }
                .padding(12)
            }
            .frame(maxHeight: 200)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
    }
    
    // MARK: - 数据处理方法
    
    /**
     * 加载测试数据
     */
    private func loadTestData() {
        // 如果没有成员，自动选择第一个
        if selectedMember == nil && !members.isEmpty {
            selectedMember = members.first
        }
    }
    
    /**
     * 处理配置保存
     */
    private func handleConfigSave(_ configData: ScratchCardConfigData) {
        guard let member = selectedMember else { return }
        
        // 保存配置
        let savedConfig = DataManager.shared.saveScratchCardConfig(
            for: member,
            cardCount: configData.cardCount,
            costPerPlay: configData.costPerPlay,
            cardPrizes: configData.cardPrizes
        )
        
        showScratchCardConfig = false
        
        if savedConfig != nil {
            addTestResult("✅ 配置保存成功")
            addTestResult("📊 卡片数: \(configData.cardCount), 积分: \(configData.costPerPlay)")
            addTestResult("🎁 奖品: \(configData.cardPrizes.joined(separator: ", "))")
        } else {
            addTestResult("❌ 配置保存失败")
        }
    }
    
    /**
     * 运行自动化测试
     */
    private func runAutomatedTest() {
        guard let member = selectedMember else { return }
        
        isRunningTest = true
        testResults.removeAll()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 测试1: 检查初始状态
            addTestResult("🔍 开始自动化测试...")
            
            let initialConfig = member.getLotteryConfig(for: .scratchcard)
            if initialConfig == nil {
                addTestResult("✅ 初始状态：无配置（符合预期）")
            } else {
                addTestResult("ℹ️ 初始状态：已有配置")
            }
            
            // 测试2: 创建测试配置
            let testPrizes = ["测试奖品1", "测试奖品2", "测试奖品3", "测试奖品4", "测试奖品5"]
            let testConfig = DataManager.shared.saveScratchCardConfig(
                for: member,
                cardCount: 5,
                costPerPlay: 15,
                cardPrizes: testPrizes
            )
            
            if testConfig != nil {
                addTestResult("✅ 测试配置创建成功")
            } else {
                addTestResult("❌ 测试配置创建失败")
                isRunningTest = false
                return
            }
            
            // 测试3: 验证配置加载
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                if let loadedConfig = member.getLotteryConfig(for: .scratchcard) {
                    addTestResult("✅ 配置加载成功")
                    addTestResult("📊 卡片数: \(loadedConfig.itemCount), 积分: \(loadedConfig.costPerPlay)")
                    
                    let loadedPrizes = loadedConfig.allItems.map { $0.formattedPrizeName }
                    addTestResult("🎁 奖品: \(loadedPrizes.joined(separator: ", "))")
                    
                    // 验证数据一致性
                    if Int(loadedConfig.itemCount) == 5 && 
                       Int(loadedConfig.costPerPlay) == 15 &&
                       loadedPrizes == testPrizes {
                        addTestResult("✅ 数据一致性验证通过")
                    } else {
                        addTestResult("❌ 数据一致性验证失败")
                    }
                } else {
                    addTestResult("❌ 配置加载失败")
                }
                
                addTestResult("🎯 自动化测试完成")
                isRunningTest = false
            }
        }
    }
    
    /**
     * 添加测试结果
     */
    private func addTestResult(_ result: String) {
        testResults.append(result)
    }
}

#Preview {
    ScratchCardConfigPersistenceTestView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
