//
//  MemberDetailView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 成员详情视图
 * 基于ztt1项目的StudentDetailView设计，适配家庭成员场景
 */
struct MemberDetailView: View {

    // MARK: - Properties
    let memberId: String?
    let onClose: () -> Void
    let onNavigateToSubscription: () -> Void

    // MARK: - ViewModel
    @StateObject private var viewModel: MemberDetailViewModel
    @EnvironmentObject private var dataManager: DataManager

    // MARK: - Animation States
    @State private var pageAppeared = false
    @State private var selectedRecordType: RecordType = .points

    // MARK: - Initialization
    init(memberId: String?, onClose: @escaping () -> Void, onNavigateToSubscription: @escaping () -> Void) {
        self.memberId = memberId
        self.onClose = onClose
        self.onNavigateToSubscription = onNavigateToSubscription
        // 暂时使用空字符串，后续修复
        self._viewModel = StateObject(wrappedValue: MemberDetailViewModel(memberId: memberId ?? "unknown"))
    }

    // MARK: - 弹窗状态管理
    @State private var showAddPointsOptions = false
    @State private var showDeductPointsOptions = false
    @State private var showAddPointsForm = false
    @State private var showDeductPointsForm = false
    @State private var showRewardExchange = false
    @State private var showAddRewardForm = false
    @State private var showCustomRewardExchange = false
    @State private var showLotteryOptions = false // 抽奖选项弹窗
    @State private var showLotteryWheel = false // 大转盘页面
    @State private var showBlindBox = false // 盲盒页面
    @State private var showScratchCard = false // 刮刮卡页面
    @State private var isAddingRuleOnly = false // 标记是否只是添加规则（不执行积分操作）
    @State private var showAIAnalysis = false // AI分析页面

    // MARK: - Record Types
    enum RecordType: String, CaseIterable {
        case points = "积分记录"
        case exchange = "兑换记录"

        var displayName: String {
            return self.rawValue
        }
    }

    // MARK: - Computed Properties

    /// 转换Core Data的MemberRule到UI需要的MemberPointsRule格式
    private var addPointsRules: [MemberPointsRule] {
        return viewModel.addPointsRules.map { rule in
            MemberPointsRule(
                id: rule.id ?? UUID(),
                name: rule.name ?? "",
                value: Int(rule.value),
                type: .add
            )
        }
    }

    private var deductPointsRules: [MemberPointsRule] {
        return viewModel.deductPointsRules.map { rule in
            MemberPointsRule(
                id: rule.id ?? UUID(),
                name: rule.name ?? "",
                value: Int(rule.value),
                type: .deduct
            )
        }
    }

    /// 转换Core Data的MemberPrize到UI需要的MemberReward格式
    private var rewards: [MemberReward] {
        return viewModel.memberPrizes.map { prize in
            MemberReward(
                id: prize.id ?? UUID(),
                name: prize.name ?? "",
                pointsCost: Int(prize.cost),
                description: prize.type,
                isCustom: true
            )
        }
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 美化背景渐变
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color(hex: "#fcfff4"), location: 0.0),
                        .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                        .init(color: Color.white, location: 0.7),
                        .init(color: Color(hex: "#fafffe"), location: 1.0)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea(.all)

                // 装饰性背景元素
                VStack {
                    HStack {
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.04))
                            .frame(width: 100, height: 100)
                            .offset(x: 30, y: 20)
                    }
                    Spacer()
                    HStack {
                        Circle()
                            .fill(Color(hex: "#FFE49E").opacity(0.05))
                            .frame(width: 80, height: 80)
                            .offset(x: -40, y: -30)
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.03))
                            .frame(width: 60, height: 60)
                            .offset(x: 20, y: 40)
                    }
                }

                VStack(spacing: 0) {
                    // 顶部关闭按钮
                    MemberDetailHeader {
                        onClose()
                    }
                    .offset(y: pageAppeared ? 0 : -80)
                    .animation(.easeInOut(duration: 0.6).delay(0.1), value: pageAppeared)

                    // 主要内容区域
                    VStack(spacing: DesignSystem.Spacing.lg) {
                        // 成员信息卡片
                        MemberInfoCard(
                            memberName: viewModel.memberName,
                            memberRole: viewModel.memberRole,
                            memberAge: viewModel.memberAge,
                            currentPoints: viewModel.currentPoints,
                            onAddPointsTapped: {
                                showAddPointsOptions = true
                            },
                            onDeductPointsTapped: {
                                showDeductPointsOptions = true
                            },
                            onExchangeTapped: {
                                showRewardExchange = true
                            },
                            onLotteryTapped: {
                                showLotteryOptions = true
                            },
                            onAnalysisReportTapped: {
                                showAIAnalysis = true
                            }
                        )
                        .padding(.horizontal, 25)
                        .opacity(pageAppeared ? 1.0 : 0.0)
                        .offset(y: pageAppeared ? 0 : 50)
                        .animation(.easeInOut(duration: 0.8).delay(0.1), value: pageAppeared)

                        // 历史记录组件
                        MemberHistoryRecordsView(
                            selectedRecordType: $selectedRecordType,
                            viewModel: viewModel
                        )
                        .padding(.horizontal, 25)
                        .opacity(pageAppeared ? 1.0 : 0.0)
                        .offset(y: pageAppeared ? 0 : 30)
                        .animation(.easeInOut(duration: 0.6).delay(0.5), value: pageAppeared)

                        Spacer()  // 填充剩余空间
                    }
                    .padding(.top, DesignSystem.Spacing.sm)
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            withAnimation(.easeInOut(duration: 0.8)) {
                pageAppeared = true
            }
        }
        // MARK: - 弹窗覆盖层
        .overlay(
            ZStack {
                // 加分选项弹窗
                if showAddPointsOptions {
                    MemberPointsOptionsView(
                        isPresented: $showAddPointsOptions,
                        operationType: .add,
                        rules: addPointsRules,
                        onRuleSelected: { rule in
                            // 直接执行加分操作
                            executePointsOperation(rule: rule)
                            showAddPointsOptions = false
                        },
                        onCustomSelected: {
                            showAddPointsOptions = false
                            isAddingRuleOnly = false // 自定义操作，需要执行积分操作
                            showAddPointsForm = true
                        },
                        onAddRuleRequested: {
                            showAddPointsOptions = false
                            isAddingRuleOnly = true // 只添加规则，不执行积分操作
                            showAddPointsForm = true
                        },
                        onRuleDeleted: { rule in
                            // 删除加分规则
                            deleteRule(rule: rule, from: .add)
                        },
                        onCancel: {
                            showAddPointsOptions = false
                        }
                    )
                }

                // 扣分选项弹窗
                if showDeductPointsOptions {
                    MemberPointsOptionsView(
                        isPresented: $showDeductPointsOptions,
                        operationType: .deduct,
                        rules: deductPointsRules,
                        onRuleSelected: { rule in
                            // 直接执行扣分操作
                            executePointsOperation(rule: rule)
                            showDeductPointsOptions = false
                        },
                        onCustomSelected: {
                            showDeductPointsOptions = false
                            isAddingRuleOnly = false // 自定义操作，需要执行积分操作
                            showDeductPointsForm = true
                        },
                        onAddRuleRequested: {
                            showDeductPointsOptions = false
                            isAddingRuleOnly = true // 只添加规则，不执行积分操作
                            showDeductPointsForm = true
                        },
                        onRuleDeleted: { rule in
                            // 删除扣分规则
                            deleteRule(rule: rule, from: .deduct)
                        },
                        onCancel: {
                            showDeductPointsOptions = false
                        }
                    )
                }

                // 加分表单弹窗
                if showAddPointsForm {
                    MemberPointsFormView(
                        isPresented: $showAddPointsForm,
                        operationType: .add,
                        isAddingRuleOnly: isAddingRuleOnly,
                        onSubmit: { formData in
                            // 处理表单提交
                            handleFormSubmission(formData: formData, operationType: .add)
                            showAddPointsForm = false
                        },
                        onCancel: {
                            showAddPointsForm = false
                        }
                    )
                }

                // 扣分表单弹窗
                if showDeductPointsForm {
                    MemberPointsFormView(
                        isPresented: $showDeductPointsForm,
                        operationType: .deduct,
                        isAddingRuleOnly: isAddingRuleOnly,
                        onSubmit: { formData in
                            // 处理表单提交
                            handleFormSubmission(formData: formData, operationType: .deduct)
                            showDeductPointsForm = false
                        },
                        onCancel: {
                            showDeductPointsForm = false
                        }
                    )
                }

                // 兑换奖品弹窗
                if showRewardExchange {
                    MemberRewardExchangeView(
                        isPresented: $showRewardExchange,
                        memberName: viewModel.memberName,
                        currentPoints: viewModel.currentPoints,
                        rewards: rewards,
                        onRewardSelected: { reward in
                            // 执行兑换操作
                            executeRewardExchange(reward: reward)
                            showRewardExchange = false
                        },
                        onCustomRewardRequested: {
                            showRewardExchange = false
                            showCustomRewardExchange = true
                        },
                        onAddRewardRequested: {
                            showRewardExchange = false
                            showAddRewardForm = true
                        },
                        onRewardDeleted: { reward in
                            // 删除奖品
                            deleteReward(reward: reward)
                        },
                        onCancel: {
                            showRewardExchange = false
                        }
                    )
                }

                // 添加奖品表单弹窗
                if showAddRewardForm {
                    AddRewardFormView(
                        isPresented: $showAddRewardForm,
                        onSubmit: { formData in
                            // 处理添加奖品
                            handleAddReward(formData: formData)
                            showAddRewardForm = false
                        },
                        onCancel: {
                            showAddRewardForm = false
                        }
                    )
                }

                // 自定义兑换表单弹窗
                if showCustomRewardExchange {
                    CustomRewardExchangeView(
                        isPresented: $showCustomRewardExchange,
                        memberName: viewModel.memberName,
                        currentPoints: viewModel.currentPoints,
                        onExchange: { rewardName, pointsCost in
                            // 执行自定义兑换
                            executeCustomRewardExchange(rewardName: rewardName, pointsCost: pointsCost)
                            showCustomRewardExchange = false
                        },
                        onCancel: {
                            showCustomRewardExchange = false
                        }
                    )
                }

                // 抽奖选项弹窗
                if showLotteryOptions {
                    LotteryOptionsView(
                        isPresented: $showLotteryOptions,
                        onWheelSelected: {
                            print("选择大转盘 - 成员: \(viewModel.memberName)")
                            showLotteryOptions = false
                            // 延迟显示大转盘，确保选项弹窗完全关闭
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                showLotteryWheel = true
                            }
                        },
                        onBlindBoxSelected: {
                            print("选择盲盒 - 成员: \(viewModel.memberName)")
                            showLotteryOptions = false
                            // 延迟显示盲盒页面，确保选项弹窗完全关闭
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                showBlindBox = true
                            }
                        },
                        onScratchCardSelected: {
                            print("选择刮刮卡 - 成员: \(viewModel.memberName)")
                            showLotteryOptions = false
                            // 延迟显示刮刮卡页面，确保选项弹窗完全关闭
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                showScratchCard = true
                            }
                        },
                        onNavigateToSubscription: {
                            print("导航到订阅页面")
                            onNavigateToSubscription()
                        }
                    )
                }
            }
        )
        // 大转盘抽奖页面
        .fullScreenCover(isPresented: $showLotteryWheel, onDismiss: {
            // 大转盘关闭时刷新数据
            viewModel.refresh()
        }) {
            NavigationView {
                LotteryWheelView(
                    isPresented: $showLotteryWheel,
                    member: viewModel.member!,
                    onLotteryComplete: handleLotteryWheelComplete,
                    onDismiss: {
                        showLotteryWheel = false
                        // 大转盘关闭时刷新数据
                        viewModel.refresh()
                    },
                    onNavigateToSettings: {
                        // TODO: 导航到抽奖配置页面
                        print("导航到抽奖配置页面")
                    }
                )
            }
            .environmentObject(dataManager)
        }
        // 盲盒页面
        .fullScreenCover(isPresented: $showBlindBox, onDismiss: {
            // 盲盒关闭时刷新数据
            viewModel.refresh()
        }) {
            NavigationView {
                BlindBoxView(
                    member: viewModel.member!,
                    onDismiss: {
                        showBlindBox = false
                        // 盲盒关闭时刷新数据
                        viewModel.refresh()
                    },
                    onNavigateToSettings: {
                        // TODO: 导航到抽奖配置页面
                        print("导航到抽奖配置页面")
                    }
                )
            }
            .environmentObject(dataManager)
        }
        // 刮刮卡页面
        .fullScreenCover(isPresented: $showScratchCard, onDismiss: {
            // 刮刮卡关闭时刷新数据
            viewModel.refresh()
        }) {
            NavigationView {
                ScratchCardView(
                    member: viewModel.member!,
                    onDismiss: {
                        showScratchCard = false
                        // 刮刮卡关闭时刷新数据
                        viewModel.refresh()
                    }
                )
            }
            .environmentObject(dataManager)
        }
        // AI分析页面
        .fullScreenCover(isPresented: $showAIAnalysis, onDismiss: {
            // AI分析页面关闭时刷新成员数据
            viewModel.refresh()
        }) {
            if let member = viewModel.member {
                AIAnalysisView(
                    member: member,
                    onDismiss: {
                        showAIAnalysis = false
                    },
                    onNavigateToSubscription: onNavigateToSubscription
                )
            }
        }
    }

    // MARK: - 业务逻辑方法

    /**
     * 执行积分操作
     */
    private func executePointsOperation(rule: MemberPointsRule) {
        let value = rule.type == .add ? rule.value : -rule.value

        // 调用ViewModel添加积分记录
        viewModel.addPointRecord(reason: rule.name, value: value)

        print("执行积分操作: \(rule.name), 变更: \(value)")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    /**
     * 处理表单提交
     */
    private func handleFormSubmission(formData: MemberPointsFormData, operationType: MemberPointsOperationType) {
        // 转换表单数据为ViewModel需要的格式
        let formItems = formData.items.compactMap { item -> MemberDetailViewModel.FormItem? in
            guard !item.name.isEmpty && !item.value.isEmpty, let value = Int(item.value) else {
                return nil
            }

            return MemberDetailViewModel.FormItem(
                reason: item.name,
                value: value,
                operationType: operationType,
                saveAsRule: isAddingRuleOnly ? true : item.saveAsRule // 如果只是添加规则，强制保存为规则
            )
        }

        if isAddingRuleOnly {
            // 只添加规则，不执行积分操作
            viewModel.addRulesOnly(items: formItems)
            print("添加规则: \(formItems.count) 项规则")
        } else {
            // 执行积分操作（可能同时保存为规则）
            viewModel.addMultiplePointRecords(items: formItems)
            print("处理表单提交: \(formItems.count) 项操作")
        }

        // 成功触觉反馈
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }

    /**
     * 删除规则
     */
    private func deleteRule(rule: MemberPointsRule, from operationType: MemberPointsOperationType) {
        // 找到对应的Core Data MemberRule对象并删除
        let coreDataRule = operationType == .add ?
            viewModel.addPointsRules.first(where: { $0.id == rule.id }) :
            viewModel.deductPointsRules.first(where: { $0.id == rule.id })

        if let ruleToDelete = coreDataRule {
            viewModel.deleteMemberRule(ruleToDelete)
        }

        print("删除规则: \(rule.name), 类型: \(operationType.displayName)")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    /**
     * 执行奖品兑换
     */
    private func executeRewardExchange(reward: MemberReward) {
        // 调用ViewModel兑换奖品
        viewModel.redeemPrize(name: reward.name, cost: reward.pointsCost)

        print("兑换奖品: \(reward.name), 消耗积分: \(reward.pointsCost)")

        // 成功触觉反馈
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }

    /**
     * 处理批量添加奖品
     */
    private func handleAddReward(formData: MemberRewardFormData) {
        // 批量创建奖品
        for item in formData.validItems {
            viewModel.createMemberPrize(
                name: item.formattedName,
                cost: item.pointsCostValue,
                description: nil // 不再支持描述字段
            )

            print("添加奖品: \(item.formattedName), 积分消耗: \(item.pointsCostValue)")
        }

        // 成功触觉反馈
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)

        print("批量添加完成，共添加 \(formData.validItems.count) 个奖品")
    }

    /**
     * 删除奖品
     */
    private func deleteReward(reward: MemberReward) {
        // 找到对应的Core Data MemberPrize对象并删除
        if let prizeToDelete = viewModel.memberPrizes.first(where: { $0.id == reward.id }) {
            viewModel.deleteMemberPrize(prizeToDelete)
        }

        print("删除奖品: \(reward.name)")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    /**
     * 执行自定义奖品兑换
     */
    private func executeCustomRewardExchange(rewardName: String, pointsCost: Int) {
        // 调用ViewModel兑换奖品
        viewModel.redeemPrize(name: rewardName, cost: pointsCost)

        print("自定义兑换: \(rewardName), 消耗积分: \(pointsCost)")

        // 成功触觉反馈
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }

    // MARK: - Lottery Operations Handlers

    /**
     * 处理大转盘抽奖完成
     */
    private func handleLotteryWheelComplete(prizeName: String, cost: Int) {
        print("大转盘抽奖完成 - 成员: \(viewModel.memberName), 奖品: \(prizeName), 消耗积分: \(cost)")

        // 注意：积分扣除和记录创建已在LotteryWheelView中处理，这里只需要刷新数据

        // 刷新视图模型数据
        viewModel.refresh()

        // 成功触觉反馈
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }
}

// MARK: - Supporting Views

/**
 * 成员详情页顶部导航组件
 */
struct MemberDetailHeader: View {
    let onClose: () -> Void

    var body: some View {
        HStack {
            Spacer()

            // 关闭按钮 - 右上角
            Button(action: onClose) {
                Image("关闭")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 30, height: 30)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
            .frame(width: 44, height: 44)  // 扩大点击区域
            .background(Color.clear)
        }
        .padding(.horizontal, 25)  // 设置两侧边距为25pt
        .padding(.top, -10)  // 减少顶部间距
    }
}

/**
 * 成员信息卡片组件
 * 基于StudentInfoCard设计，适配家庭成员场景
 */
struct MemberInfoCard: View {

    let memberName: String
    let memberRole: String
    let memberAge: Int
    let currentPoints: Int
    let onAddPointsTapped: () -> Void
    let onDeductPointsTapped: () -> Void
    let onExchangeTapped: () -> Void
    let onLotteryTapped: () -> Void
    let onAnalysisReportTapped: () -> Void

    var body: some View {
        ZStack {
            // 卡片背景
            RoundedRectangle(cornerRadius: 25)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#edf6d9"),
                            Color(hex: "#e8f1d4")
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)

            // 装饰性背景元素
            Circle()
                .fill(Color(hex: "#B5E36B").opacity(0.08))
                .frame(width: 60, height: 60)
                .offset(x: 120, y: -80)

            Circle()
                .fill(Color(hex: "#FFE49E").opacity(0.06))
                .frame(width: 45, height: 45)
                .offset(x: -90, y: 60)

            Circle()
                .fill(Color(hex: "#74c07f").opacity(0.05))
                .frame(width: 35, height: 35)
                .offset(x: 100, y: 50)

            // 成员头像 - 左上角
            HStack {
                VStack {
                    ZStack {
                        // 头像背景光圈
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#a9d051").opacity(0.1),
                                        Color.clear
                                    ]),
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 50
                                )
                            )
                            .frame(width: 100, height: 100)

                        // 根据角色显示对应头像
                        Image(getAvatarImageName(for: memberRole))
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 80, height: 80)
                            .clipShape(Circle())
                    }
                    .padding(.top, DesignSystem.Spacing.md)
                    .padding(.leading, DesignSystem.Spacing.md)
                    Spacer()
                }
                Spacer()
            }

            // 成员信息 - 中上区域（头像右侧）
            HStack {
                Spacer()
                VStack(alignment: .leading, spacing: 4) {
                    // 成员姓名
                    Text(memberName)
                        .font(.system(size: 30, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .shadow(color: Color.white.opacity(0.5), radius: 1, x: 0, y: 1)
                        .padding(.top, DesignSystem.Spacing.md)

                    // 角色和年龄信息
                    Text("\(memberRole) · \(memberAge)岁")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Spacer()
                }
                Spacer()
            }

            // 积分显示 - 右下角
            HStack {
                Spacer()
                VStack {
                    Spacer()
                    ZStack {
                        // 积分背景光圈
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#74c07f").opacity(0.1),
                                        Color.clear
                                    ]),
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 60
                                )
                            )
                            .frame(width: 120, height: 120)

                        Text("\(currentPoints)")
                            .font(.custom("Impact", size: 90))
                            .foregroundColor(DesignSystem.Colors.scoreDisplay)
                            .shadow(color: Color(hex: "#74c07f").opacity(0.3), radius: 4, x: 0, y: 2)
                    }
                    .padding(.bottom, DesignSystem.Spacing.md)
                    .padding(.trailing, DesignSystem.Spacing.md)
                    .offset(y: 20)  // 向下移动20pt
                }
            }

            // 上排操作按钮 - 左下区域上方
            HStack {
                VStack {
                    Spacer()
                    MemberTopActionButtons(
                        onAddPointsTapped: onAddPointsTapped,
                        onDeductPointsTapped: onDeductPointsTapped,
                        buttonSize: 50,
                        spacing: 5,
                        cornerRadius: 5
                    )
                    .padding(.bottom, 70)  // 为下排按钮留出空间
                    .padding(.leading, DesignSystem.Spacing.md)
                }
                Spacer()
            }

            // 下排操作按钮 - 左下角
            HStack {
                VStack {
                    Spacer()
                    MemberBottomActionButtons(
                        memberRole: memberRole,
                        onExchangeTapped: onExchangeTapped,
                        onLotteryTapped: onLotteryTapped,
                        onAnalysisReportTapped: onAnalysisReportTapped,
                        buttonSize: 50,
                        spacing: 5,
                        cornerRadius: 5
                    )
                    .padding(.bottom, DesignSystem.Spacing.md)
                    .padding(.leading, DesignSystem.Spacing.md)
                }
                Spacer()
            }
        }
        .frame(height: 280)
    }

    /**
     * 根据角色获取头像图片名称
     */
    private func getAvatarImageName(for role: String) -> String {
        switch role {
        case "爸爸":
            return "爸爸头像"
        case "妈妈":
            return "妈妈头像"
        case "儿子":
            return "男生头像"
        case "女儿":
            return "女生头像"
        default:
            return "其他头像"
        }
    }
}

/**
 * 成员历史记录组件
 * 基于HistoryRecordsView设计，适配家庭成员场景
 */
struct MemberHistoryRecordsView: View {

    @Binding var selectedRecordType: MemberDetailView.RecordType
    @ObservedObject var viewModel: MemberDetailViewModel
    @State private var recordsAppeared = false

    var body: some View {
        VStack(spacing: 0) {
            // 选项卡切换
            MemberRecordTabSelector(
                selectedType: $selectedRecordType,
                onSelectionChanged: { type in
                    selectedRecordType = type
                }
            )
            .opacity(recordsAppeared ? 1.0 : 0.0)
            .offset(y: recordsAppeared ? 0 : -20)
            .animation(.easeInOut(duration: 0.5).delay(0.1), value: recordsAppeared)

            // 记录列表
            MemberRecordsList(
                recordType: selectedRecordType,
                recordsAppeared: $recordsAppeared,
                viewModel: viewModel
            )
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#f8f8f8"),
                    Color(hex: "#ededed")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .cornerRadius(25)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        .onAppear {
            withAnimation {
                recordsAppeared = true
            }
        }
    }
}

/**
 * 记录类型选择器
 */
struct MemberRecordTabSelector: View {
    @Binding var selectedType: MemberDetailView.RecordType
    let onSelectionChanged: (MemberDetailView.RecordType) -> Void

    var body: some View {
        HStack(spacing: 0) {
            ForEach(MemberDetailView.RecordType.allCases, id: \.self) { type in
                Button(action: {
                    selectedType = type
                    onSelectionChanged(type)
                }) {
                    VStack(spacing: 0) {
                        Text(type.displayName)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(selectedType == type ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(selectedType == type ? Color.white : Color.clear)
                            )

                        // 选中状态下显示绿色底线
                        if selectedType == type {
                            Rectangle()
                                .fill(Color(hex: "a9d051"))
                                .frame(width: 70, height: 5)
                                .cornerRadius(2)
                        }
                    }
                }
            }
        }
        .padding(.horizontal, DesignSystem.Spacing.md)
        .padding(.top, DesignSystem.Spacing.md)
        .padding(.bottom, DesignSystem.Spacing.sm)
    }
}

/**
 * 记录列表组件
 */
struct MemberRecordsList: View {
    let recordType: MemberDetailView.RecordType
    @Binding var recordsAppeared: Bool
    @ObservedObject var viewModel: MemberDetailViewModel

    var body: some View {
        if recordType == .points {
            // 积分记录
            if viewModel.pointRecords.isEmpty {
                MemberEmptyRecordsView()
            } else {
                // 记录列表 - 使用List支持swipeActions
                List {
                    ForEach(Array(viewModel.pointRecords.sorted(by: { $0.timestamp ?? Date() > $1.timestamp ?? Date() }).enumerated()), id: \.element.id) { index, record in
                        MemberPointRecordCard(record: record)
                        .opacity(recordsAppeared ? 1.0 : 0.0)
                        .offset(y: recordsAppeared ? 0 : -20)
                        .animation(.easeInOut(duration: 0.4).delay(0.05 * Double(index)), value: recordsAppeared)
                        .listRowSeparator(.hidden)
                        .listRowBackground(Color.clear)
                        .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))
                        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                            Button(action: {
                                // 添加触觉反馈
                                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                                impactFeedback.impactOccurred()

                                // 删除记录
                                viewModel.deletePointRecord(record)
                            }) {
                                Label("删除", systemImage: "trash")
                            }
                            .tint(Color(hex: "#FF6B6B"))
                        }
                    }
                }
                .listStyle(PlainListStyle())
                .background(Color.clear)
                .padding(.vertical, DesignSystem.Spacing.sm)
            }
        } else {
            // 兑换记录
            if viewModel.redemptionRecords.isEmpty {
                MemberEmptyRecordsView()
            } else {
                // 记录列表 - 使用List支持swipeActions
                List {
                    ForEach(Array(viewModel.redemptionRecords.enumerated()), id: \.element.id) { index, record in
                        MemberExchangeRecordCard(record: record)
                            .opacity(recordsAppeared ? 1.0 : 0.0)
                            .offset(y: recordsAppeared ? 0 : -20)
                            .animation(.easeInOut(duration: 0.4).delay(0.05 * Double(index)), value: recordsAppeared)
                            .listRowSeparator(.hidden)
                            .listRowBackground(Color.clear)
                            .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))
                            .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                                Button(action: {
                                    // 添加触觉反馈
                                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                                    impactFeedback.impactOccurred()

                                    // 删除记录
                                    viewModel.deleteExchangeRecord(record)
                                }) {
                                    Label("删除", systemImage: "trash")
                                }
                                .tint(Color(hex: "#FF6B6B"))
                            }
                    }
                }
                .listStyle(PlainListStyle())
                .background(Color.clear)
                .padding(.vertical, DesignSystem.Spacing.sm)
            }
        }
    }
}

/**
 * 积分记录卡片
 */
struct MemberPointRecordCard: View {
    let record: PointRecord

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // 历史图标容器
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "#f8ffe5"),
                                Color(hex: "#edf6d9")
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 40, height: 40)
                    .shadow(color: Color(hex: "#a9d051").opacity(0.1), radius: 2, x: 0, y: 1)

                Image("lishi")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)
                    .foregroundColor(Color(hex: "#a9d051"))
            }

            // 记录信息
            VStack(alignment: .leading, spacing: 4) {
                Text(record.reason ?? "未知原因")
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)

                HStack(spacing: 8) {
                    Text(formatDate(record.timestamp ?? Date()))
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    // 类型标识
                    Text("积分")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(Color(hex: "#a9d051"))
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color(hex: "#a9d051").opacity(0.15))
                        )
                }
            }

            Spacer()

            // 积分变化大圆角矩形按钮
            ZStack {
                RoundedRectangle(cornerRadius: 14)
                    .fill(record.value >= 0 ? Color(hex: "#26C34B") : Color(hex: "#FF5B5B"))
                    .frame(width: 44, height: 32)

                Text("\(record.value >= 0 ? "+" : "")\(record.value)")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 12)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.white,
                    Color(hex: "#fafafa")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.08), radius: 4, x: 0, y: 2)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke((record.value >= 0 ? Color(hex: "#26C34B") : Color(hex: "#FF5B5B")).opacity(0.1), lineWidth: 1)
        )
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: date)
    }
}

/**
 * 兑换记录卡片（包含抽奖记录）
 */
struct MemberExchangeRecordCard: View {
    let record: MemberDetailViewModel.ExchangeRecord

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // 兑换图标容器
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "#f8ffe5"),
                                Color(hex: "#edf6d9")
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 40, height: 40)
                    .shadow(color: Color(hex: record.sourceColor).opacity(0.1), radius: 2, x: 0, y: 1)

                Image(getIconName())
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)
                    .foregroundColor(Color(hex: record.sourceColor))
            }

            // 记录信息
            VStack(alignment: .leading, spacing: 4) {
                Text(record.prizeName)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)

                HStack(spacing: 8) {
                    Text(formatDate(record.timestamp))
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    // 类型标识
                    Text(record.sourceDisplayName)
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(Color(hex: record.sourceColor))
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color(hex: record.sourceColor).opacity(0.15))
                        )
                }
            }

            Spacer()

            // 消耗积分大圆角矩形按钮
            ZStack {
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(hex: "#FF8C00"))
                    .frame(width: 44, height: 32)

                Text("-\(record.cost)")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 12)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.white,
                    Color(hex: "#fafafa")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.08), radius: 4, x: 0, y: 2)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#FF8C00").opacity(0.1), lineWidth: 1)
        )
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: date)
    }

    private func getIconName() -> String {
        switch record.recordType {
        case .redemption:
            return "lingqujilu"
        case .lottery:
            return "choujiang"
        }
    }
}

/**
 * 空状态视图
 */
struct MemberEmptyRecordsView: View {
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // 图标容器
            ZStack {
                Circle()
                    .fill(DesignSystem.Colors.historyBackground.opacity(0.6))
                    .frame(width: 80, height: 80)

                Image("lishi")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 40, height: 40)
                    .foregroundColor(DesignSystem.Colors.textSecondary.opacity(0.7))
            }

            VStack(spacing: 8) {
                Text("暂无记录")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("学生的积分变动记录将在此显示")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }
}

/**
 * 上排操作按钮组件（加分、扣分）
 */
struct MemberTopActionButtons: View {

    let onAddPointsTapped: () -> Void
    let onDeductPointsTapped: () -> Void
    let buttonSize: CGFloat
    let spacing: CGFloat
    let cornerRadius: CGFloat

    var body: some View {
        HStack(spacing: spacing) {
            MemberCustomActionButton(
                title: "加分",
                systemIcon: "plus",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onAddPointsTapped
            )

            MemberCustomActionButton(
                title: "扣分",
                systemIcon: "minus",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onDeductPointsTapped
            )
        }
    }
}

/**
 * 下排操作按钮组件（兑换、抽奖、分析报告）
 */
struct MemberBottomActionButtons: View {

    let memberRole: String
    let onExchangeTapped: () -> Void
    let onLotteryTapped: () -> Void
    let onAnalysisReportTapped: () -> Void
    let buttonSize: CGFloat
    let spacing: CGFloat
    let cornerRadius: CGFloat

    var body: some View {
        HStack(spacing: spacing) {
            MemberCustomActionButton(
                title: "兑换",
                imageName: "lingqujilu",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onExchangeTapped
            )

            MemberCustomActionButton(
                title: "抽奖",
                imageName: "choujiang",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onLotteryTapped
            )

            // AI分析按钮只在角色为"儿子"或"女儿"时显示
            if shouldShowAnalysisButton {
                MemberCustomActionButton(
                    title: "分析",
                    imageName: "fenxi",
                    size: buttonSize,
                    cornerRadius: cornerRadius,
                    action: onAnalysisReportTapped
                )
            }
        }
    }

    /**
     * 判断是否应该显示AI分析按钮
     * 只有角色为"儿子"或"女儿"时才显示
     */
    private var shouldShowAnalysisButton: Bool {
        return memberRole == "儿子" || memberRole == "女儿"
    }
}

/**
 * 可配置的操作按钮组件
 */
struct MemberCustomActionButton: View {
    let title: String
    let systemIcon: String?
    let imageName: String?
    let size: CGFloat
    let cornerRadius: CGFloat
    let action: () -> Void

    @State private var isPressed = false

    init(title: String, systemIcon: String, size: CGFloat, cornerRadius: CGFloat, action: @escaping () -> Void) {
        self.title = title
        self.systemIcon = systemIcon
        self.imageName = nil
        self.size = size
        self.cornerRadius = cornerRadius
        self.action = action
    }

    init(title: String, imageName: String, size: CGFloat, cornerRadius: CGFloat, action: @escaping () -> Void) {
        self.title = title
        self.systemIcon = nil
        self.imageName = imageName
        self.size = size
        self.cornerRadius = cornerRadius
        self.action = action
    }

    var body: some View {
        Button(action: {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()

            // 按压动画
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }

            // 执行动作
            action()

            // 重置按压状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
        }) {
            VStack(spacing: 4) {
                // 图标区域 - 固定高度确保对齐
                Group {
                    if let systemIcon = systemIcon {
                        Image(systemName: systemIcon)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    } else if let imageName = imageName {
                        Image(imageName)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 16, height: 16)
                            .foregroundColor(.white)
                    }
                }
                .frame(width: 16, height: 16) // 统一图标区域大小

                // 标题区域 - 固定高度和居中对齐确保一致性
                Text(title)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.white)
                    .lineLimit(1)
                    .frame(width: size - 8, height: 12, alignment: .center) // 固定文字区域大小和居中对齐
                    .minimumScaleFactor(0.8) // 允许文字缩放以适应空间
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center) // 确保整个内容在按钮中居中
        }
        .frame(width: size, height: size)
        .background(DesignSystem.Colors.actionButtonBackground)
        .cornerRadius(cornerRadius)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
    }
}

// MARK: - Preview
#Preview {
    MemberDetailView(
        memberId: "test-member-id",
        onClose: {},
        onNavigateToSubscription: {}
    )
}
