//
//  MarkdownTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025-08-01.
//  测试Markdown渲染功能
//

import SwiftUI

/**
 * Markdown渲染测试视图
 * 用于验证AI分析报告中的markdown格式是否正确显示
 */
struct MarkdownTestView: View {
    
    // 测试用的markdown内容，模拟AI分析报告
    private let testMarkdownContent = """
    # 成长分析报告

    亲爱的家长朋友：

    感谢您如此用心地记录孩子的成长点滴。从您的观察日记中，我能感受到您对孩子成长的关注与爱。现在，让我们一起来解读这些珍贵的成长片段。

    ## 情绪变化分析

    从记录中可以看到，孩子的情绪呈现出健康的波动状态：

    - **积极情绪触发点**：学业成就感（8.1作业表扬）、助人行为（7.25/7.31）、兴趣探索（7.29数学/7.22艺术）、社交成功（7.21）
    - **消极情绪触发点**：学业压力（7.28考试）、社交冲突（7.30争执）、自主权受限（7.24叛逆表现）

    特别值得注意的是，孩子已经展现出良好的情绪调节能力：
    - 7.30冲突后能主动道歉
    - 7.28考试压力后次日即恢复积极状态
    - 7.24叛逆日之后连续出现亲社会行为

    这符合8岁儿童情绪发展的典型特征：情绪体验更丰富，但调节能力快速提升。

    ### 建议与指导

    基于以上分析，我们建议：

    1. **继续鼓励积极行为**：当孩子表现出助人、学习等积极行为时，及时给予肯定
    2. **情绪教育**：帮助孩子认识和表达自己的情绪
    3. **建立规则边界**：在给予自主权的同时，保持必要的规则和边界

    ## 总结

    您的孩子正在健康成长，展现出良好的**社交能力**和**情绪调节能力**。继续保持现在的教育方式，相信孩子会越来越优秀！
    """
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 原始文本显示
                    VStack(alignment: .leading, spacing: 12) {
                        Text("原始文本（未渲染）")
                            .font(.headline)
                            .foregroundColor(.secondary)
                        
                        Text(testMarkdownContent)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                    }
                    
                    Divider()
                    
                    // Markdown渲染后的显示
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Markdown渲染后")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        MarkdownText(content: testMarkdownContent)
                            .padding()
                            .background(Color(.systemBackground))
                            .cornerRadius(12)
                            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                    }
                }
                .padding()
            }
            .navigationTitle("Markdown测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

// MARK: - 预览

struct MarkdownTestView_Previews: PreviewProvider {
    static var previews: some View {
        MarkdownTestView()
            .preferredColorScheme(.light)
        
        MarkdownTestView()
            .preferredColorScheme(.dark)
    }
}
