//
//  ProfileView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 个人中心视图
 */
struct ProfileView: View {

    @State private var showScratchCardTest = false
    @State private var showMarkdownTest = false

    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // 顶部标题
            HStack {
                Text("profile.title".localized)
                    .font(.system(
                        size: DesignSystem.Typography.HeadingLarge.fontSize,
                        weight: DesignSystem.Typography.HeadingLarge.fontWeight
                    ))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
            .padding(.top, DesignSystem.Spacing.lg)
            
            // 用户信息卡片
            VStack(spacing: DesignSystem.Spacing.md) {
                HStack(spacing: DesignSystem.Spacing.md) {
                    // 头像
                    Circle()
                        .fill(DesignSystem.Colors.primary)
                        .frame(width: 60, height: 60)
                        .overlay(
                            Image(systemName: "person.fill")
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 30, height: 30)
                                .foregroundColor(.white)
                        )
                    
                    VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                        Text("用户昵称")
                            .font(.system(
                                size: DesignSystem.Typography.HeadingMedium.fontSize,
                                weight: DesignSystem.Typography.HeadingMedium.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Text("免费用户")
                            .font(.system(
                                size: DesignSystem.Typography.Caption.fontSize,
                                weight: DesignSystem.Typography.Caption.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(DesignSystem.Spacing.md)
                .background(DesignSystem.Colors.profileUserInfoBackground)
                .cornerRadius(DesignSystem.CornerRadius.medium)
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
            
            // 订阅管理卡片
            VStack(spacing: DesignSystem.Spacing.md) {
                HStack {
                    Text("profile.subscription.title".localized)
                        .font(.system(
                            size: DesignSystem.Typography.HeadingMedium.fontSize,
                            weight: DesignSystem.Typography.HeadingMedium.fontWeight
                        ))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Spacer()
                }
                
                VStack(spacing: DesignSystem.Spacing.sm) {
                    HStack {
                        Text("profile.subscription.current_status".localized)
                            .font(.system(
                                size: DesignSystem.Typography.Body.fontSize,
                                weight: DesignSystem.Typography.Body.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Spacer()
                        
                        Text("免费版")
                            .font(.system(
                                size: DesignSystem.Typography.Body.fontSize,
                                weight: DesignSystem.Typography.Body.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                    }
                    
                    Button(action: {
                        // TODO: 实现升级会员功能
                    }) {
                        Text("profile.subscription.upgrade".localized)
                            .font(.system(
                                size: DesignSystem.Typography.Body.fontSize,
                                weight: DesignSystem.Typography.Body.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textButton)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, DesignSystem.Spacing.sm)
                            .background(DesignSystem.Colors.profileSubscriptionButtonBackground)
                            .cornerRadius(DesignSystem.CornerRadius.medium)
                    }
                }
                .padding(DesignSystem.Spacing.md)
                .background(DesignSystem.Colors.profileSubscriptionBannerBackground)
                .cornerRadius(DesignSystem.CornerRadius.medium)
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
            
            // 系统设置列表
            VStack(spacing: DesignSystem.Spacing.md) {
                HStack {
                    Text("profile.settings.title".localized)
                        .font(.system(
                            size: DesignSystem.Typography.HeadingMedium.fontSize,
                            weight: DesignSystem.Typography.HeadingMedium.fontWeight
                        ))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Spacer()
                }
                .padding(.horizontal, DesignSystem.Spacing.md)
                
                VStack(spacing: 0) {
                    SettingsRow(
                        icon: "globe",
                        title: "profile.settings.language".localized,
                        action: {
                            // TODO: 实现语言切换功能
                        }
                    )
                    
                    Divider()
                        .padding(.leading, 50)
                    
                    SettingsRow(
                        icon: "clock.arrow.circlepath",
                        title: "profile.settings.history".localized,
                        action: {
                            // TODO: 实现历史记录功能
                        }
                    )
                    
                    Divider()
                        .padding(.leading, 50)
                    
                    SettingsRow(
                        icon: "questionmark.circle",
                        title: "profile.settings.help".localized,
                        action: {
                            // TODO: 实现帮助功能
                        }
                    )
                    
                    Divider()
                        .padding(.leading, 50)
                    
                    SettingsRow(
                        icon: "info.circle",
                        title: "profile.settings.about".localized,
                        action: {
                            // TODO: 实现关于页面功能
                        }
                    )

                    Divider()
                        .padding(.leading, 50)

                    SettingsRow(
                        icon: "testtube.2",
                        title: "刮刮卡配置测试",
                        action: {
                            showScratchCardTest = true
                        }
                    )

                    Divider()
                        .padding(.leading, 50)

                    SettingsRow(
                        icon: "doc.text",
                        title: "Markdown渲染测试",
                        action: {
                            showMarkdownTest = true
                        }
                    )

                    Divider()
                        .padding(.leading, 50)
                    
                    SettingsRow(
                        icon: "trash",
                        title: "profile.settings.delete_account".localized,
                        titleColor: DesignSystem.Colors.errorColor,
                        action: {
                            // TODO: 实现删除账号功能
                        }
                    )
                    
                    Divider()
                        .padding(.leading, 50)
                    
                    SettingsRow(
                        icon: "rectangle.portrait.and.arrow.right",
                        title: "profile.settings.logout".localized,
                        action: {
                            // TODO: 实现退出登录功能
                        }
                    )
                }
                .background(DesignSystem.Colors.profileSettingsItemBackground)
                .cornerRadius(DesignSystem.CornerRadius.medium)
                .padding(.horizontal, DesignSystem.Spacing.md)
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(DesignSystem.Colors.background)
        .sheet(isPresented: $showScratchCardTest) {
            ScratchCardConfigPersistenceTestView()
        }
        .sheet(isPresented: $showMarkdownTest) {
            MarkdownTestView()
        }
    }
}

// MARK: - Settings Row Component
private struct SettingsRow: View {
    let icon: String
    let title: String
    let titleColor: Color
    let action: () -> Void
    
    init(icon: String, title: String, titleColor: Color = DesignSystem.Colors.profileSettingsTextColor, action: @escaping () -> Void) {
        self.icon = icon
        self.title = title
        self.titleColor = titleColor
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: icon)
                    .frame(width: 20, height: 20)
                    .foregroundColor(DesignSystem.Colors.profileSettingsIconColor)
                
                Text(title)
                    .font(.system(
                        size: DesignSystem.Typography.Body.fontSize,
                        weight: DesignSystem.Typography.Body.fontWeight
                    ))
                    .foregroundColor(titleColor)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(DesignSystem.Colors.profileSettingsArrowColor)
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
            .padding(.vertical, DesignSystem.Spacing.md)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    ProfileView()
}
