<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22758" systemVersion="23G93" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="NO" userDefinedModelVersionIdentifier="">
    <!-- 用户实体 -->
    <entity name="User" representedClassName="User" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="nickname" optional="YES" attributeType="String"/>
        <attribute name="email" optional="YES" attributeType="String"/>
        <attribute name="appleUserID" optional="YES" attributeType="String"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="subscription" optional="YES" maxCount="1" deletionRule="Cascade" destinationEntity="Subscription" inverseName="user" inverseEntity="Subscription"/>
        <relationship name="members" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Member" inverseName="user" inverseEntity="Member"/>
        <relationship name="globalRules" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="GlobalRule" inverseName="user" inverseEntity="GlobalRule"/>
    </entity>

    <!-- 订阅信息实体 -->
    <entity name="Subscription" representedClassName="Subscription" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="subscriptionType" optional="YES" attributeType="String" defaultValueString="free"/> <!-- free/basic/premium -->
        <attribute name="isActive" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="startDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="endDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="productIdentifier" optional="YES" attributeType="String"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="subscription" inverseEntity="User"/>
    </entity>

    <!-- 全局规则实体 -->
    <entity name="GlobalRule" representedClassName="GlobalRule" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="value" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String"/> <!-- add/deduct -->
        <attribute name="isFrequent" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="globalRules" inverseEntity="User"/>
    </entity>

    <!-- 家庭成员实体 -->
    <entity name="Member" representedClassName="Member" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="role" optional="YES" attributeType="String"/> <!-- father/mother/son/daughter/other -->
        <attribute name="birthDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="memberNumber" optional="YES" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="currentPoints" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="avatar" optional="YES" attributeType="String"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="members" inverseEntity="User"/>
        <relationship name="pointRecords" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="PointRecord" inverseName="member" inverseEntity="PointRecord"/>
        <relationship name="diaryEntries" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="DiaryEntry" inverseName="member" inverseEntity="DiaryEntry"/>
        <relationship name="redemptionRecords" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="RedemptionRecord" inverseName="member" inverseEntity="RedemptionRecord"/>
        <relationship name="lotteryRecords" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="LotteryRecord" inverseName="member" inverseEntity="LotteryRecord"/>
        <relationship name="aiReports" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="AIReport" inverseName="member" inverseEntity="AIReport"/>
        <relationship name="memberRules" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="MemberRule" inverseName="member" inverseEntity="MemberRule"/>
        <relationship name="memberPrizes" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="MemberPrize" inverseName="member" inverseEntity="MemberPrize"/>
        <relationship name="lotteryConfigs" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="LotteryConfig" inverseName="member" inverseEntity="LotteryConfig"/>
    </entity>

    <!-- 积分记录实体 -->
    <entity name="PointRecord" representedClassName="PointRecord" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="reason" optional="YES" attributeType="String"/>
        <attribute name="value" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="recordType" optional="YES" attributeType="String" defaultValueString="behavior"/> <!-- behavior/redemption/lottery -->
        <attribute name="isReversed" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <relationship name="member" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Member" inverseName="pointRecords" inverseEntity="Member"/>
    </entity>

    <!-- 成长日记实体 -->
    <entity name="DiaryEntry" representedClassName="DiaryEntry" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="title" optional="YES" attributeType="String"/>
        <attribute name="content" optional="YES" attributeType="String"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="member" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Member" inverseName="diaryEntries" inverseEntity="Member"/>
    </entity>

    <!-- AI报告实体 -->
    <entity name="AIReport" representedClassName="AIReport" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="title" optional="YES" attributeType="String"/>
        <attribute name="content" optional="YES" attributeType="String"/>
        <attribute name="reportType" optional="YES" attributeType="String" defaultValueString="analysis"/> <!-- analysis/growth -->
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="inputDataSummary" optional="YES" attributeType="String"/>
        <attribute name="totalRecords" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="positiveRecords" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="negativeRecords" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="member" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Member" inverseName="aiReports" inverseEntity="Member"/>
    </entity>

    <!-- 成员规则实体 -->
    <entity name="MemberRule" representedClassName="MemberRule" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="value" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String"/> <!-- add/deduct -->
        <attribute name="isFrequent" optional="YES" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="member" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Member" inverseName="memberRules" inverseEntity="Member"/>
    </entity>

    <!-- 成员奖品实体 -->
    <entity name="MemberPrize" representedClassName="MemberPrize" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="cost" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="type" optional="YES" attributeType="String"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="member" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Member" inverseName="memberPrizes" inverseEntity="Member"/>
    </entity>

    <!-- 兑换记录实体 -->
    <entity name="RedemptionRecord" representedClassName="RedemptionRecord" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="prizeName" optional="YES" attributeType="String"/>
        <attribute name="cost" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="source" optional="YES" attributeType="String" defaultValueString="redemption"/> <!-- redemption/lottery -->
        <relationship name="member" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Member" inverseName="redemptionRecords" inverseEntity="Member"/>
    </entity>

    <!-- 抽奖记录实体 -->
    <entity name="LotteryRecord" representedClassName="LotteryRecord" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="toolType" optional="YES" attributeType="String"/> <!-- wheel/blindbox/scratchcard -->
        <attribute name="prizeResult" optional="YES" attributeType="String"/>
        <attribute name="cost" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="member" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Member" inverseName="lotteryRecords" inverseEntity="Member"/>
    </entity>

    <!-- 抽奖配置实体 -->
    <entity name="LotteryConfig" representedClassName="LotteryConfig" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="toolType" optional="YES" attributeType="String"/> <!-- wheel/blindbox/scratchcard -->
        <attribute name="itemCount" optional="YES" attributeType="Integer 32" defaultValueString="8" usesScalarValueType="YES"/>
        <attribute name="costPerPlay" optional="YES" attributeType="Integer 32" defaultValueString="10" usesScalarValueType="YES"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="member" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Member" inverseName="lotteryConfigs" inverseEntity="Member"/>
        <relationship name="items" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="LotteryItem" inverseName="lotteryConfig" inverseEntity="LotteryItem"/>
    </entity>

    <!-- 抽奖项目实体 -->
    <entity name="LotteryItem" representedClassName="LotteryItem" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="itemIndex" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="prizeName" optional="YES" attributeType="String"/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="lotteryConfig" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="LotteryConfig" inverseName="items" inverseEntity="LotteryConfig"/>
    </entity>

    <elements>
        <element name="User" positionX="-63" positionY="-18" width="128" height="163"/>
        <element name="Subscription" positionX="117" positionY="-18" width="128" height="178"/>
        <element name="GlobalRule" positionX="297" positionY="-18" width="128" height="148"/>
        <element name="Member" positionX="-63" positionY="198" width="128" height="298"/>
        <element name="PointRecord" positionX="117" positionY="198" width="128" height="148"/>
        <element name="DiaryEntry" positionX="297" positionY="198" width="128" height="133"/>
        <element name="AIReport" positionX="477" positionY="198" width="128" height="193"/>
        <element name="MemberRule" positionX="-63" positionY="548" width="128" height="148"/>
        <element name="MemberPrize" positionX="117" positionY="548" width="128" height="118"/>
        <element name="RedemptionRecord" positionX="297" positionY="548" width="128" height="118"/>
        <element name="LotteryRecord" positionX="477" positionY="548" width="128" height="118"/>
        <element name="LotteryConfig" positionX="-63" positionY="718" width="128" height="163"/>
        <element name="LotteryItem" positionX="117" positionY="718" width="128" height="103"/>
    </elements>
</model>