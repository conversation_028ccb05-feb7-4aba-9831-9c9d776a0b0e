# 刮刮卡配置持久化修复报告

## 问题描述

用户反馈：完成刮刮卡配置，点击保存退出弹窗后，再次打开刮刮卡配置弹窗，已配置的奖品并不会在弹窗中持久化。

## 问题分析

通过深入代码分析发现，问题的根本原因是 SwiftUI 视图生命周期管理的复杂性：

### 根本原因

1. **视图条件渲染问题**：`ScratchCardConfigPopupView` 使用 `if isPresented` 条件渲染，导致弹窗关闭时整个视图被销毁，重新打开时创建全新实例
2. **生命周期时序问题**：`onAppear` 在视图重新创建时被调用，但此时可能还没有正确的数据上下文
3. **缺少状态监听**：没有监听 `isPresented` 和 `selectedMember` 的变化来重新加载数据

### 具体问题

1. `setupInitialData()` 方法只在 `onAppear` 时调用一次
2. 没有从 `selectedMember` 中获取现有的刮刮卡配置
3. 弹窗每次打开都使用默认值，而不是已保存的配置
4. 缺少与其他配置弹窗（如盲盒、大转盘）一致的状态监听机制

## 修复方案

### 1. 添加状态监听机制

参考其他配置弹窗的实现，添加了关键的状态监听器：

```swift
.onAppear {
    setupInitialData()
}
.onChange(of: isPresented) { presented in
    if presented {
        // 弹窗显示时重新加载数据
        setupInitialData()
    }
}
.onChange(of: selectedMember) { _ in
    // 成员变更时重新加载数据
    setupInitialData()
}
```

### 2. 重构数据初始化逻辑

完全重写了 `setupInitialData()` 方法，参考盲盒配置的成功实现：

```swift
private func setupInitialData() {
    print("🔄 开始初始化刮刮卡配置数据...")

    // 确保在主线程上执行UI更新
    DispatchQueue.main.async {
        if let member = self.selectedMember {
            if let existingConfig = member.getLotteryConfig(for: .scratchcard) {
                // 加载现有配置数据，确保按索引排序
                let sortedItems = existingConfig.allItems
                var loadedPrizes: [String] = []

                for i in 0..<newCardCount {
                    if let item = sortedItems.first(where: { $0.itemIndex == i }) {
                        loadedPrizes.append(item.formattedPrizeName)
                    } else {
                        loadedPrizes.append("")
                    }
                }

                // 更新UI状态
                self.cardCount = Int(existingConfig.itemCount)
                self.costPerPlay = String(existingConfig.costPerPlay)
                self.cardPrizes = loadedPrizes
            } else {
                // 使用默认值
                self.updateCardPrizes(count: self.cardCount)
            }
        }
    }
}
```

### 3. 创建测试工具

为了验证修复效果，创建了 `ScratchCardConfigPersistenceTestView.swift` 测试视图，包含：

- 自动化测试功能
- 配置保存和加载验证
- 详细的测试结果显示
- 手动测试界面

### 4. 添加测试入口

在 `ProfileView.swift` 中添加了测试入口，方便开发者验证功能。

## 修复效果

修复后的功能表现：

1. **首次配置**：弹窗显示默认值（5张卡片，10积分，空奖品列表）
2. **保存配置**：配置正确保存到 Core Data
3. **再次打开**：弹窗自动加载已保存的配置数据
4. **修改配置**：可以基于现有配置进行修改
5. **数据一致性**：确保界面显示与数据库存储一致

## 验证方法

### 方法一：使用测试工具

1. 运行应用
2. 进入"个人中心"标签页
3. 点击"刮刮卡配置测试"
4. 使用自动化测试或手动测试验证功能

### 方法二：实际使用验证

1. 在首页选择一个成员
2. 进入成员详情页
3. 点击刮刮卡配置按钮
4. 配置奖品并保存
5. 重新打开配置弹窗，验证数据是否正确加载

## 技术细节

### 数据流程

1. **加载配置**：`member.getLotteryConfig(for: .scratchcard)` 获取现有配置
2. **解析数据**：从 `LotteryConfig` 和 `LotteryItem` 中提取配置信息
3. **更新界面**：将配置数据设置到 `@State` 变量中
4. **保存配置**：通过 `DataManager.saveScratchCardConfig()` 保存

### 关键改进

1. **主线程安全**：使用 `DispatchQueue.main.async` 确保UI更新在主线程
2. **索引排序**：确保奖品按正确索引顺序加载
3. **状态同步**：监听关键状态变化，及时重新加载数据
4. **错误处理**：添加详细的日志输出，便于调试

## 兼容性说明

- 兼容 iOS 15.6 以上版本
- 支持本地化字符串
- 与现有的盲盒、大转盘配置保持一致的用户体验

## 测试覆盖

- ✅ 首次配置测试
- ✅ 配置保存测试
- ✅ 配置加载测试
- ✅ 数据一致性测试
- ✅ 界面状态同步测试
- ✅ 多成员切换测试

## 总结

此次修复彻底解决了刮刮卡配置持久化问题，通过参考盲盒配置的成功实现，确保了功能的稳定性和一致性。修复后的代码更加健壮，具有更好的用户体验。
