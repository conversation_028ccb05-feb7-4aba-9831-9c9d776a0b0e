//
//  ztt2App.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import SwiftUI

@main
struct ztt2App: App {
    let persistenceController = PersistenceController.shared
    @StateObject private var dataManager = DataManager.shared

    init() {
        // 初始化API密钥
        setupAPIKey()
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(dataManager)
        }
    }

    // MARK: - Private Methods

    /**
     * 设置API密钥
     */
    private func setupAPIKey() {
        let keychainManager = KeychainManager.shared

        // 如果Keychain中没有API密钥，则设置默认密钥
        if !keychainManager.hasAPIKey() {
            keychainManager.saveAPIKey("sk-eb2dc94c4f594097b7747421169b9110")
            print("🔐 已设置默认API密钥")
        } else {
            print("🔐 API密钥已存在于Keychain中")
        }
    }
}
